import { useState, useEffect } from 'react'

export type Theme = 'day' | 'night'

export const useTheme = () => {
  const [theme, setTheme] = useState<Theme>('day')

  useEffect(() => {
    // Check for saved theme preference or default to time-based theme
    const savedTheme = localStorage.getItem('theme') as Theme | null
    const currentHour = new Date().getHours()
    
    // Default to night theme between 6 PM and 6 AM
    const defaultTheme: Theme = (currentHour >= 18 || currentHour < 6) ? 'night' : 'day'
    
    const initialTheme = savedTheme || defaultTheme
    setTheme(initialTheme)
    document.body.setAttribute('data-theme', initialTheme)
  }, [])

  const toggleTheme = () => {
    const newTheme: Theme = theme === 'day' ? 'night' : 'day'
    setTheme(newTheme)
    document.body.setAttribute('data-theme', newTheme)
    localStorage.setItem('theme', newTheme)
  }

  const setSpecificTheme = (newTheme: Theme) => {
    setTheme(newTheme)
    document.body.setAttribute('data-theme', newTheme)
    localStorage.setItem('theme', newTheme)
  }

  return {
    theme,
    toggleTheme,
    setSpecificTheme,
    isDayTheme: theme === 'day',
    isNightTheme: theme === 'night'
  }
}
