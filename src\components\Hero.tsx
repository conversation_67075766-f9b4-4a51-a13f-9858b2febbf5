import React from 'react'

interface HeroProps {
  theme: 'day' | 'night'
}

const Hero: React.FC<HeroProps> = ({ theme }) => {
  const scrollToMenu = () => {
    const menuElement = document.getElementById('menu')
    if (menuElement) {
      menuElement.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section className="hero">
      <div className="hero-overlay"></div>
      <div className="hero-content">
        <h1>Don Fancies</h1>
        <p className="hero-subtitle">
          {theme === 'day' 
            ? 'Your Perfect Café Experience Awaits' 
            : 'Where the Night Comes Alive'
          }
        </p>
        <button className="button hero-button" onClick={scrollToMenu}>
          {theme === 'day' ? 'Explore Our Menu' : 'Discover the Night'}
        </button>
        <div className="mode-hint">
          <p>
            {theme === 'day' 
              ? 'Enjoying our cozy café atmosphere? ' 
              : 'Ready for the nightlife? '
            }
            <span className="hint-highlight">
              {theme === 'day' 
                ? 'Switch to Night mode to see our pub side!' 
                : 'Switch to Day mode to see our café side!'
              }
            </span>
          </p>
        </div>
      </div>
    </section>
  )
}

export default Hero
