import React, { useState } from 'react'
import { eventsData } from '../data/eventsData'
import Modal from './Modal'
import ContactForm from './ContactForm'

const Events: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState<number | null>(null)

  const handleBooking = (eventId: number) => {
    setSelectedEvent(eventId)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setSelectedEvent(null)
  }

  return (
    <section id="events" className="events">
      <div className="container">
        <h2 className="text-center">Upcoming Events</h2>
        <p className="events-subtitle text-center">
          Join us for special events, live music, and themed nights
        </p>
        
        <div className="events-list">
          {eventsData.map(event => (
            <div key={event.id} className="event-card">
              <div className="event-details">
                <h3 className="event-title">{event.title}</h3>
                <div className="event-date">{event.date}</div>
                <div className="event-time">{event.time}</div>
                <p className="event-description">{event.description}</p>
                <button 
                  className="book-btn"
                  onClick={() => handleBooking(event.id)}
                >
                  Book Now
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        title="Make a Reservation"
      >
        <ContactForm onClose={closeModal} />
      </Modal>
    </section>
  )
}

export default Events
