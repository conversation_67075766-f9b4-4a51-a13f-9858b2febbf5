export interface MenuItem {
  id: number
  name: string
  price: string
  description: string
  category: string
}

export const menuData: MenuItem[] = [
  // Coffee & Tea
  {
    id: 1,
    name: "Signature Espresso",
    price: "3.50",
    description: "Rich, bold espresso made from our house blend of premium beans",
    category: "coffee"
  },
  {
    id: 2,
    name: "Cappuccino",
    price: "4.25",
    description: "Perfect balance of espresso, steamed milk, and velvety foam",
    category: "coffee"
  },
  {
    id: 3,
    name: "Caramel Macchiato",
    price: "5.00",
    description: "Vanilla syrup, steamed milk, espresso, and caramel drizzle",
    category: "coffee"
  },
  {
    id: 4,
    name: "Earl Grey Tea",
    price: "3.00",
    description: "Classic bergamot-infused black tea, served with honey and lemon",
    category: "coffee"
  },

  // Breakfast
  {
    id: 5,
    name: "Avocado Toast",
    price: "8.50",
    description: "Smashed avocado on sourdough with cherry tomatoes, feta, and microgreens",
    category: "breakfast"
  },
  {
    id: 6,
    name: "French Toast",
    price: "9.00",
    description: "Thick-cut brioche with maple syrup, fresh berries, and whipped cream",
    category: "breakfast"
  },
  {
    id: 7,
    name: "Breakfast Burrito",
    price: "10.50",
    description: "Scrambled eggs, bacon, cheese, potatoes, and salsa in a flour tortilla",
    category: "breakfast"
  },

  // Lunch
  {
    id: 8,
    name: "Grilled Chicken Sandwich",
    price: "12.00",
    description: "Herb-marinated chicken breast with lettuce, tomato, and aioli on ciabatta",
    category: "lunch"
  },
  {
    id: 9,
    name: "Caesar Salad",
    price: "10.00",
    description: "Crisp romaine, parmesan, croutons, and house-made caesar dressing",
    category: "lunch"
  },
  {
    id: 10,
    name: "Quinoa Bowl",
    price: "11.50",
    description: "Quinoa, roasted vegetables, chickpeas, and tahini dressing",
    category: "lunch"
  },

  // Drinks (Evening)
  {
    id: 11,
    name: "Craft Beer Selection",
    price: "6.00",
    description: "Rotating selection of local craft beers on tap",
    category: "drinks"
  },
  {
    id: 12,
    name: "Don's Signature Cocktail",
    price: "12.00",
    description: "House special with bourbon, honey, lemon, and fresh herbs",
    category: "drinks"
  },
  {
    id: 13,
    name: "Wine by the Glass",
    price: "8.00",
    description: "Curated selection of red and white wines",
    category: "drinks"
  },

  // Dinner
  {
    id: 14,
    name: "Fish & Chips",
    price: "16.00",
    description: "Beer-battered cod with hand-cut fries and mushy peas",
    category: "dinner"
  },
  {
    id: 15,
    name: "Ribeye Steak",
    price: "24.00",
    description: "12oz ribeye with garlic mashed potatoes and seasonal vegetables",
    category: "dinner"
  },
  {
    id: 16,
    name: "Mushroom Risotto",
    price: "18.00",
    description: "Creamy arborio rice with wild mushrooms and truffle oil",
    category: "dinner"
  }
]
