import React, { useState } from 'react'
import { menuData } from '../data/menuData'

const Menu: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('all')

  const categories = [
    { id: 'all', name: 'All Items' },
    { id: 'coffee', name: 'Coffee & Tea' },
    { id: 'breakfast', name: 'Breakfast' },
    { id: 'lunch', name: 'Lunch' },
    { id: 'drinks', name: 'Drinks' },
    { id: 'dinner', name: 'Dinner' }
  ]

  const filteredItems = activeCategory === 'all' 
    ? menuData 
    : menuData.filter(item => item.category === activeCategory)

  return (
    <section id="menu" className="menu">
      <div className="container">
        <h2 className="text-center">Our Menu</h2>
        <p className="menu-subtitle text-center">
          From morning coffee to evening cocktails, discover our carefully crafted offerings
        </p>
        
        <div className="menu-categories">
          {categories.map(category => (
            <button
              key={category.id}
              className={`category-button ${activeCategory === category.id ? 'active' : ''}`}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>

        <div className="menu-items">
          {filteredItems.map(item => (
            <div key={item.id} className="menu-item">
              <div className="menu-item-details">
                <h3 className="menu-item-name">{item.name}</h3>
                <div className="menu-item-price">${item.price}</div>
                <p className="menu-item-description">{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Menu
