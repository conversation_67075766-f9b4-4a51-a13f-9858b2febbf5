/* Import the built CSS from dist folder for now */
/* This will be replaced with the actual CSS content */
@import url('../dist/assets/index-DCl8e-pM.css');

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;
  padding: 1rem;
}

.modal-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--card-shadow);
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  color: var(--text-color);
  font-family: var(--heading-font);
}

.modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-color);
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.modal-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.modal-content {
  padding: 1.5rem 2rem 2rem;
}

/* Responsive Modal */
@media (max-width: 768px) {
  .modal-container {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .modal-header {
    padding: 1rem 1.5rem 0.5rem;
  }

  .modal-content {
    padding: 1rem 1.5rem 1.5rem;
  }
}

/* Smooth Animations */
.modal-overlay {
  animation: fadeIn 0.3s ease-out;
}

.modal-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Lightbox Animations */
.lightbox-overlay {
  animation: fadeIn 0.3s ease-out;
}

.lightbox-container {
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Mobile Navigation */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.hamburger {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  position: relative;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: var(--text-color);
  margin-bottom: 4px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger span:last-child {
  margin-bottom: 0;
}

.hamburger-open span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-open span:nth-child(2) {
  opacity: 0;
}

.hamburger-open span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }

  .nav-menu {
    position: fixed;
    top: var(--header-height);
    left: 0;
    width: 100%;
    height: calc(100vh - var(--header-height));
    background-color: var(--primary-color);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
  }

  .nav-menu-open {
    transform: translateX(0);
  }

  .nav-menu li {
    margin: 1rem 0;
  }

  .nav-menu a {
    font-size: 1.2rem;
    padding: 1rem;
  }

  .desktop-toggle-container {
    margin: 2rem 0;
  }
}

/* Additional custom styles if needed */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}
