import React, { useEffect, useRef } from 'react'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

const Visit: React.FC = () => {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<L.Map | null>(null)

  useEffect(() => {
    if (mapRef.current && !mapInstanceRef.current) {
      // Initialize the map
      mapInstanceRef.current = L.map(mapRef.current).setView([40.7128, -74.0060], 15) // NYC coordinates as example

      // Add tile layer
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(mapInstanceRef.current)

      // Add marker
      const marker = L.marker([40.7128, -74.0060]).addTo(mapInstanceRef.current)
      marker.bindPopup(`
        <div class="map-marker-popup">
          <h4>Don <PERSON></h4>
          <p>Your favorite café by day, pub by night</p>
        </div>
      `)
    }

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [])

  return (
    <section id="visit" className="visit">
      <div className="container">
        <h2 className="text-center">Visit Us</h2>
        
        <div className="visit-content">
          <div className="hours-card">
            <h3>Opening Hours</h3>
            <div className="hours-info">
              <div className="hours-list">
                <div className="hours-item">
                  <span className="day">Monday - Friday</span>
                  <span className="hours">7:00 AM - 11:00 PM</span>
                </div>
                <div className="hours-item">
                  <span className="day">Saturday</span>
                  <span className="hours">8:00 AM - 12:00 AM</span>
                </div>
                <div className="hours-item">
                  <span className="day">Sunday</span>
                  <span className="hours">8:00 AM - 10:00 PM</span>
                </div>
              </div>
              <p className="hours-note">
                Café service until 6 PM, pub service from 6 PM onwards
              </p>
            </div>
          </div>

          <div className="location-card">
            <h3>Location & Contact</h3>
            <address>
              <span className="address-number">123</span> Main Street<br />
              Downtown District<br />
              New York, NY 10001
            </address>
            
            <div className="contact-info">
              <p><strong>Phone:</strong> <span className="phone-number">(*************</span></p>
              <p><strong>Email:</strong> <EMAIL></p>
            </div>
            
            <div className="map-container">
              <div ref={mapRef} style={{ width: '100%', height: '250px' }}></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Visit
