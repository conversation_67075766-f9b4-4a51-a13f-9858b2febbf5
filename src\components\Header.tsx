import React, { useState } from 'react'

interface HeaderProps {
  theme: 'day' | 'night'
  onThemeToggle: () => void
}

const Header: React.FC<HeaderProps> = ({ theme, onThemeToggle }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setIsMobileMenuOpen(false) // Close mobile menu after navigation
  }

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <header className="header">
      <div className="container header-container">
        <div className="logo">
          <h1>Don Fancies</h1>
          <p className="tagline">Café by Day, Pub by Night</p>
        </div>
        
        <nav className="nav">
          <ul className={`nav-menu ${isMobileMenuOpen ? 'nav-menu-open' : ''}`}>
            <li><a href="#about" onClick={(e) => { e.preventDefault(); scrollToSection('about') }}>About</a></li>
            <li><a href="#menu" onClick={(e) => { e.preventDefault(); scrollToSection('menu') }}>Menu</a></li>
            <li><a href="#events" onClick={(e) => { e.preventDefault(); scrollToSection('events') }}>Events</a></li>
            <li><a href="#gallery" onClick={(e) => { e.preventDefault(); scrollToSection('gallery') }}>Gallery</a></li>
            <li><a href="#visit" onClick={(e) => { e.preventDefault(); scrollToSection('visit') }}>Visit</a></li>
            <li>
              <div className="desktop-toggle-container">
                <button className="theme-toggle" onClick={onThemeToggle}>
                  <span className="toggle-text">
                    {theme === 'day' ? 'Switch to Night' : 'Switch to Day'}
                  </span>
                </button>
              </div>
            </li>
          </ul>

          <button className="mobile-menu-toggle" onClick={toggleMobileMenu}>
            <span className={`hamburger ${isMobileMenuOpen ? 'hamburger-open' : ''}`}>
              <span></span>
              <span></span>
              <span></span>
            </span>
          </button>
        </nav>
      </div>
    </header>
  )
}

export default Header
