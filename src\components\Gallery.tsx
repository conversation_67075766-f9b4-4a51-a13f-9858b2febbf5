import React, { useState, useEffect } from 'react'
import { galleryData } from '../data/galleryData'

const Gallery: React.FC = () => {
  const [lightboxOpen, setLightboxOpen] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  const openLightbox = (index: number) => {
    setCurrentImageIndex(index)
    setLightboxOpen(true)
  }

  const closeLightbox = () => {
    setLightboxOpen(false)
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % galleryData.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + galleryData.length) % galleryData.length)
  }

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!lightboxOpen) return

      switch (e.key) {
        case 'Escape':
          closeLightbox()
          break
        case 'ArrowLeft':
          prevImage()
          break
        case 'ArrowRight':
          nextImage()
          break
      }
    }

    document.addEventListener('keydown', handleKeyPress)
    return () => document.removeEventListener('keydown', handleKeyPress)
  }, [lightboxOpen])

  return (
    <section id="gallery" className="gallery">
      <div className="container">
        <h2 className="text-center">Gallery</h2>
        <p className="gallery-subtitle text-center">
          Take a peek at our atmosphere, food, and memorable moments
        </p>
        
        <div className="gallery-grid">
          {galleryData.map((image, index) => (
            <div 
              key={image.id} 
              className="gallery-item"
              onClick={() => openLightbox(index)}
            >
              <img src={image.src} alt={image.alt} />
              <div className="gallery-overlay">
                <p>{image.caption}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {lightboxOpen && (
        <div
          className={`lightbox-overlay ${document.body.getAttribute('data-theme') === 'night' ? 'night-mode' : ''}`}
          onClick={closeLightbox}
        >
          <div className="lightbox-container" onClick={(e) => e.stopPropagation()}>
            <button className="lightbox-close" onClick={closeLightbox}>×</button>
            <button className="lightbox-nav lightbox-prev" onClick={prevImage}>‹</button>
            <button className="lightbox-nav lightbox-next" onClick={nextImage}>›</button>
            <img
              className="lightbox-image"
              src={galleryData[currentImageIndex].src}
              alt={galleryData[currentImageIndex].alt}
            />
            <div className="lightbox-caption">
              {galleryData[currentImageIndex].caption}
            </div>
          </div>
        </div>
      )}
    </section>
  )
}

export default Gallery
