import React from 'react'
import Header from './components/Header'
import Hero from './components/Hero'
import About from './components/About'
import Menu from './components/Menu'
import Events from './components/Events'
import Gallery from './components/Gallery'
import Visit from './components/Visit'
import Footer from './components/Footer'
import { useTheme } from './hooks/useTheme'

function App() {
  const { theme, toggleTheme } = useTheme()

  return (
    <div className="App">
      <Header theme={theme} onThemeToggle={toggleTheme} />
      <Hero theme={theme} />
      <About />
      <Menu />
      <Events />
      <Gallery />
      <Visit />
      <Footer />
    </div>
  )
}

export default App
