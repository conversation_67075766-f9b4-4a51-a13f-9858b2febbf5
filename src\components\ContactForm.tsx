import React, { useState } from 'react'
import emailjs from '@emailjs/browser'

interface ContactFormProps {
  onClose: () => void
}

interface FormData {
  name: string
  email: string
  phone: string
  date: string
  time: string
  guests: string
  message: string
}

const ContactForm: React.FC<ContactFormProps> = ({ onClose }) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    date: '',
    time: '',
    guests: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [error, setError] = useState('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      // EmailJS configuration - you would need to set up your own service
      const templateParams = {
        from_name: formData.name,
        from_email: formData.email,
        phone: formData.phone,
        date: formData.date,
        time: formData.time,
        guests: formData.guests,
        message: formData.message,
        to_name: 'Don Fancies'
      }

      // Replace with your actual EmailJS service ID, template ID, and public key
      await emailjs.send(
        'YOUR_SERVICE_ID',
        'YOUR_TEMPLATE_ID',
        templateParams,
        'YOUR_PUBLIC_KEY'
      )

      setIsSubmitted(true)
    } catch (err) {
      setError('Failed to send message. Please try again or call us directly.')
      console.error('EmailJS error:', err)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubmitted) {
    return (
      <div className="booking-success">
        <h3>Reservation Request Sent!</h3>
        <p>Thank you for your interest in Don Fancies. We'll get back to you within 24 hours to confirm your reservation.</p>
        <button className="btn btn-primary" onClick={onClose}>
          Close
        </button>
      </div>
    )
  }

  return (
    <form className="booking-form" onSubmit={handleSubmit}>
      <div className="form-group">
        <label htmlFor="name">Full Name *</label>
        <input
          type="text"
          id="name"
          name="name"
          className="form-control"
          value={formData.name}
          onChange={handleChange}
          required
          placeholder="Your full name"
        />
      </div>

      <div className="form-group">
        <label htmlFor="email">Email *</label>
        <input
          type="email"
          id="email"
          name="email"
          className="form-control"
          value={formData.email}
          onChange={handleChange}
          required
          placeholder="<EMAIL>"
        />
      </div>

      <div className="form-group">
        <label htmlFor="phone">Phone Number</label>
        <input
          type="tel"
          id="phone"
          name="phone"
          className="form-control"
          value={formData.phone}
          onChange={handleChange}
          placeholder="(*************"
        />
      </div>

      <div className="form-group">
        <label htmlFor="date">Preferred Date *</label>
        <input
          type="date"
          id="date"
          name="date"
          className="form-control"
          value={formData.date}
          onChange={handleChange}
          required
          min={new Date().toISOString().split('T')[0]}
        />
      </div>

      <div className="form-group">
        <label htmlFor="time">Preferred Time *</label>
        <select
          id="time"
          name="time"
          className="form-control"
          value={formData.time}
          onChange={handleChange}
          required
        >
          <option value="">Select a time</option>
          <option value="7:00 AM">7:00 AM</option>
          <option value="8:00 AM">8:00 AM</option>
          <option value="9:00 AM">9:00 AM</option>
          <option value="10:00 AM">10:00 AM</option>
          <option value="11:00 AM">11:00 AM</option>
          <option value="12:00 PM">12:00 PM</option>
          <option value="1:00 PM">1:00 PM</option>
          <option value="2:00 PM">2:00 PM</option>
          <option value="3:00 PM">3:00 PM</option>
          <option value="4:00 PM">4:00 PM</option>
          <option value="5:00 PM">5:00 PM</option>
          <option value="6:00 PM">6:00 PM</option>
          <option value="7:00 PM">7:00 PM</option>
          <option value="8:00 PM">8:00 PM</option>
          <option value="9:00 PM">9:00 PM</option>
          <option value="10:00 PM">10:00 PM</option>
        </select>
      </div>

      <div className="form-group">
        <label htmlFor="guests">Number of Guests *</label>
        <select
          id="guests"
          name="guests"
          className="form-control"
          value={formData.guests}
          onChange={handleChange}
          required
        >
          <option value="">Select party size</option>
          <option value="1">1 Guest</option>
          <option value="2">2 Guests</option>
          <option value="3">3 Guests</option>
          <option value="4">4 Guests</option>
          <option value="5">5 Guests</option>
          <option value="6">6 Guests</option>
          <option value="7">7 Guests</option>
          <option value="8">8 Guests</option>
          <option value="9+">9+ Guests</option>
        </select>
      </div>

      <div className="form-group">
        <label htmlFor="message">Special Requests</label>
        <textarea
          id="message"
          name="message"
          className="form-control"
          value={formData.message}
          onChange={handleChange}
          placeholder="Any special requests, dietary restrictions, or occasion details..."
          rows={4}
        />
      </div>

      {error && <div className="form-error">{error}</div>}

      <div className="form-actions">
        <button type="button" className="btn btn-secondary" onClick={onClose}>
          Cancel
        </button>
        <button 
          type="submit" 
          className="btn btn-primary" 
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Sending...' : 'Send Reservation Request'}
        </button>
      </div>
    </form>
  )
}

export default ContactForm
